"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useSearchParams } from "next/navigation";
import { TranscriptionProvider } from "@/contexts/TranscriptionContext";
import DashboardV2 from "@/components/Dashboard/DashboardV2";
import DashboardMobile from "@/components/Dashboard/DashboardMobile";
import DashboardLayout from "@/components/Dashboard/DashboardLayout";

export default function Page() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const searchParams = useSearchParams();
  const [selectedFolderId, setSelectedFolderId] = useState("all");
  const dashboardRef = useRef(null);

  // 从URL参数初始化selectedFolderId
  useEffect(() => {
    const folderParam = searchParams.get("folder");
    if (folderParam) {
      setSelectedFolderId(folderParam);
    } else {
      // 没有folder参数时，表示选中Home，统一使用"all"表示获取所有转录记录
      setSelectedFolderId("all");
    }
  }, [searchParams]);

  const handleFolderChange = useCallback((folderId) => {
    setSelectedFolderId(folderId);
  }, []);

  // 退出批量模式的函数
  const handleExitBatchMode = useCallback(() => {
    if (dashboardRef.current?.fileListRef?.current) {
      dashboardRef.current.fileListRef.current.exitBatchMode();
    }
  }, []);

  return (
    <TranscriptionProvider>
      {isMobile ? (
        <DashboardMobile />
      ) : (
        <DashboardLayout
          onFolderChange={handleFolderChange}
          onSidebarClick={handleExitBatchMode}
        >
          <DashboardV2 ref={dashboardRef} selectedFolderId={selectedFolderId} />
        </DashboardLayout>
      )}
    </TranscriptionProvider>
  );
}
