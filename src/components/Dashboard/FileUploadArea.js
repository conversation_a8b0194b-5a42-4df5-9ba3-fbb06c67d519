"use client";

import React, { useRef, useState, useCallback, useEffect } from "react";
import { useTranslations } from "next-intl";
import { debounce } from "lodash";
import { Link } from "@/components/Common/Link";

import { useAuthStore } from "@/stores/useAuthStore";
import { useEntitlementsStore } from "@/stores/useEntitlementsStore";
import { TranscriptionLimitDisplay } from "@/components/Dashboard/TranscriptionLimitDisplay";
import { checkTranscriptionLimit } from "@/lib/transcriptionUtils";
import {
  isValidFileType,
  calculateFileDuration,
  validateFileSize,
} from "@/lib/fileUtils";
import { SUPPORTED_FORMATS_ACCEPT } from "@/constants/file";
import TrustPilotPromo from "@/components/Dashboard/TrustPilotPromo";
import { useFileUploadEngine } from "@/hooks/useFileUploadEngine";
import { transcriptionService } from "@/services/api/transcriptionService";
import { getErrorMessage, reportUploadError } from "@/lib/utils";

// 导入新的组件
import FileDisplay from "@/components/Common/FileUploader/FileDisplay";
import FileUploadStatus from "@/components/Common/FileUploader/FileUploadStatus";
import DeleteConfirmDialog from "@/components/Common/FileUploader/DeleteConfirmDialog";
import UploadPrompt from "@/components/Common/FileUploader/UploadPrompt";
import { trackEvent } from "@/lib/analytics";

const FileUploadArea = ({
  onUploadSuccess,
  onClose,
  onFileStatusChange,
  showDeleteConfirmFromParent,
  onDeleteConfirmResult,
  className,
  selectedFolderId = null,
}) => {
  const t = useTranslations("dashboard.fileUpload");
  const t_common = useTranslations("common");
  const fileInputRef = useRef(null);
  const fileDisplayRef = useRef(null); // 添加FileDisplay的ref

  const {
    selectedFile,
    setSelectedFile,
    uploadProgress,
    uploadState,
    fileDuration,
    setFileDuration,
    transcriptionFileId,
    setTranscriptionFileId,
    showAlert,
    errorMessage,
    processingProgress,
    processing,

    // 新增：网速和剩余时间状态
    uploadSpeed,
    estimatedTimeRemaining,
    isMultipartUpload,

    uploadFile,
    cancelUpload,
    setShowAlert,
    setErrorMessage,
    setUploadState,
  } = useFileUploadEngine();

  const { user } = useAuthStore();
  const { summary, fetchEntitlements } = useEntitlementsStore();

  useEffect(() => {
    fetchEntitlements();
  }, [fetchEntitlements]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // 通知父组件文件状态变化
  useEffect(() => {
    if (onFileStatusChange !== undefined) {
      onFileStatusChange(selectedFile !== null);
    }
  }, [selectedFile, onFileStatusChange]);

  // 处理从父组件传来的删除确认对话框显示状态
  useEffect(() => {
    if (showDeleteConfirmFromParent !== undefined) {
      setShowDeleteConfirm(showDeleteConfirmFromParent);
    }
  }, [showDeleteConfirmFromParent]);

  const handleUploadClick = (e) => {
    if (e) {
      e.stopPropagation(); // 阻止事件冒泡
    }
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 添加这个辅助函数
  const showError = useCallback(
    (message, autoHide = true) => {
      setErrorMessage(message);
      setShowAlert(true);

      if (autoHide) {
        setTimeout(() => {
          setShowAlert(false);
        }, 3000);
      }
    },
    [setErrorMessage, setShowAlert]
  );

  // 1. 添加共同的文件处理逻辑
  const handleFileProcess = useCallback(
    async (file) => {
      // 清除之前的错误状态
      setShowAlert(false);
      setErrorMessage("");

      try {
        // 检查文件类型
        const isValid = isValidFileType(file);
        if (!isValid) {
          // 打点记录无效文件类型
          trackEvent("invalid_file_type_detected", {
            fileType: file.type || "unknown",
          });

          showError(
            t("alerts.unsupportedFileType", {
              fileType: file.type || "unknown",
            })
          );
          return;
        }

        // 检查文件大小 - 对所有引擎都执行检查
        const sizeValidation = validateFileSize(file, t_common);

        // 对于所有引擎，如果文件超过 5GB，直接阻止上传
        if (!sizeValidation.isValid) {
          // For files exceeding 5GB, show error with tutorial link
          if (sizeValidation.showTutorialLink) {
            const tutorialMessage = (
              <div className="text-center">
                <div className="mb-2 text-red-600">
                  {sizeValidation.message}{" "}
                  {t_common("fileUploadStatus.tutorial.errorExplanation")}
                </div>
                <Link
                  href="/blog/extract-audio-from-video-vlc-guide?utm_source=file_upload_area"
                  target="_blank"
                  className="text-custom-bg hover:text-custom-bg/80 underline"
                >
                  {t_common("fileUploadStatus.tutorial.viewTutorial")}
                </Link>
              </div>
            );
            setErrorMessage(tutorialMessage);
            setShowAlert(true);
          } else {
            showError(sizeValidation.message);
          }
          return;
        }

        const duration = await calculateFileDuration(file);

        // 只有当 duration 不为 null 时才进行时长检查
        if (duration !== null) {
          // 免费用户120分钟限制检查
          const maxFreeDurationMinutes = 120; // 免费用户最大时长（分钟）
          const maxFreeDuration = maxFreeDurationMinutes * 60; // 转换为秒
          if (duration > maxFreeDuration && !user?.hasPaidPlan) {
            showError(
              t("alerts.freeUserDurationLimitExceeded", {
                maxMinutes: maxFreeDurationMinutes,
              })
            );
            return;
          }

          // 检查转录限制
          if (!checkTranscriptionLimit(duration, summary)) {
            showError(t("alerts.transcriptionLimitExceeded"));
            return;
          }
        }

        // 设置文件和时长
        setSelectedFile(file);
        setFileDuration(duration);
        setUploadState("preparing");

        // 对于 2GB-5GB 的文件，警告会在 FileDisplay 组件中显示
        // 这里不需要额外的警告逻辑

        // 自动开始上传
        const fileId = await uploadFile(file, duration, selectedFolderId);

        if (fileId) {
          // 设置转录文件ID
          setTranscriptionFileId(fileId);

          // 通知FileDisplay文件处理完成，应用待处理的Speaker Recognition设置
          if (fileDisplayRef.current?.onProcessingComplete) {
            fileDisplayRef.current.onProcessingComplete(fileId);
          }
        }
      } catch (error) {
        // 显示通用错误信息
        const errorMessage = error?.message || String(error);
        const isNetworkError = errorMessage.includes("Network Error");
        // 确保 error 参数是字符串，而不是对象
        const errorText =
          typeof error?.data?.message === "object"
            ? JSON.stringify(error.data.message)
            : error?.data?.message || errorMessage;

        showError(
          isNetworkError
            ? t("alerts.networkError")
            : t("alerts.processingFailedWithContact", {
                error: errorText,
              }),
          false
        );
        trackEvent("upload_error", {
          fileType: file.type,
          errorMessage: getErrorMessage(error),
        });
        reportUploadError({
          error,
          fileType: file.type,
          fileName: file.name,
          fileSize: file.size,
          userId: user?.id,
          userEmail: user?.email,
        });
      } finally {
        // 确保文件输入字段总是被重置
        if (fileInputRef.current) {
          setTimeout(() => {
            fileInputRef.current.value = "";
          }, 100);
        }
      }
    },
    [
      t,
      user,
      summary,
      setSelectedFile,
      setFileDuration,
      setUploadState,
      uploadFile,
      setTranscriptionFileId,
      showError,
      setShowAlert,
      setErrorMessage,
    ]
  );

  // 2. 简化后的 handleFileChange
  const handleFileChange = async (e) => {
    e.stopPropagation();
    const file = e.target.files[0];
    if (!file) return;

    await handleFileProcess(file);
  };

  // 3. 简化后的 handleDrop
  const handleDrop = useCallback(
    async (e) => {
      e.preventDefault();
      // 添加检查：如果已有选中文件，则不处理拖放的新文件
      if (selectedFile !== null) return;

      const file = e.dataTransfer.files[0];
      if (!file) return;

      await handleFileProcess(file);
    },
    [selectedFile, handleFileProcess]
  ); // 添加selectedFile到依赖数组

  const handleRemoveFile = () => {
    setShowDeleteConfirm(true);
  };

  const confirmRemoveFile = () => {
    cancelUpload();
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    setShowDeleteConfirm(false);

    // 如果有转录文件ID，删除服务器上的文件
    if (transcriptionFileId) {
      transcriptionService.deleteTranscription(transcriptionFileId);
      setTranscriptionFileId(null);
    }

    // 通知父组件确认结果
    if (onDeleteConfirmResult) {
      onDeleteConfirmResult(true);
    }
  };

  // 将原函数变成防抖函数
  const handleTranscribeClick = useCallback(
    debounce(async () => {
      try {
        // 不在这里触发转录，只需获取转录文件的数据
        if (transcriptionFileId) {
          // 调用onUploadSuccess回调，传递转录文件数据
          // 注意：这里只需传递包含id的对象，后续处理会在Dashboard中完成
          onUploadSuccess({
            id: transcriptionFileId,
          });
        } else {
          throw new Error("No transcription file ID");
        }
      } catch (error) {
        showError(
          t("alerts.transcribeFailed") +
            ". Any questions, please contact us: <EMAIL>",
          false
        );
      }
    }, 300), // 300ms 的防抖延迟
    [transcriptionFileId, onUploadSuccess, showError, t]
  );

  // 构建错误警告数据
  const alertData = {
    showAlert,
    errorMessage,
    errorAction: null,
    onErrorAction: null,
  };

  // 添加统一的区域点击处理函数
  const handleAreaClick = (e) => {
    e.stopPropagation();

    if (selectedFile !== null) return;

    handleUploadClick();
  };

  return (
    <div className={`w-full flex flex-col items-center ${className || ""}`}>
      {/* 文件上传区域 */}
      <div
        className={`border-2 border-dashed border-custom-bg/60 rounded-lg p-5 sm:p-8 ${
          selectedFile === null ? "cursor-pointer" : ""
        } transition-colors duration-200 hover:border-custom-bg mb-4 w-[90%] sm:w-full`}
        onClick={handleAreaClick}
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
      >
        {selectedFile === null ? (
          <UploadPrompt
            isLoggedIn={!!user}
            onButtonClick={handleUploadClick}
            variant="dashboard"
          />
        ) : (
          <FileDisplay
            ref={fileDisplayRef}
            file={selectedFile}
            fileDuration={fileDuration}
            onRemove={handleRemoveFile}
            transcriptionFileId={transcriptionFileId}
          />
        )}
      </div>

      {/* 关键修改：将FileUploadStatus包裹在相同宽度的div中 */}
      <div className="w-[90%] sm:w-full">
        <FileUploadStatus
          uploadState={uploadState}
          uploadProgress={uploadProgress}
          processingProgress={processingProgress}
          processing={processing}
          onTranscribeClick={handleTranscribeClick}
          showAlert={alertData.showAlert}
          errorMessage={alertData.errorMessage}
          errorAction={alertData.errorAction}
          onErrorAction={alertData.onErrorAction}
          selectedFile={selectedFile}
          uploadSpeed={uploadSpeed}
          estimatedTimeRemaining={estimatedTimeRemaining}
          isMultipartUpload={isMultipartUpload}
          selectedFolderId={selectedFolderId}
        />
      </div>

      {/* trustpilot 活动展示 */}
      {/* {!user?.hasPaidPlan && (
        <div className="hidden sm:block mt-4 w-[90%] sm:w-full">
          <TrustPilotPromo />
        </div>
      )} */}

      {/* 转录限制显示 */}
      <div className="w-[90%] sm:w-full">
        <TranscriptionLimitDisplay />
      </div>

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={(open) => {
          setShowDeleteConfirm(open);
          if (!open && onDeleteConfirmResult) {
            onDeleteConfirmResult(false);
          }
        }}
        onConfirm={confirmRemoveFile}
      />

      {/* 隐藏的文件输入 */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept={SUPPORTED_FORMATS_ACCEPT}
        onChange={handleFileChange}
        onClick={(e) => {
          // 清除当前值，确保相同文件可以重新选择
          e.currentTarget.value = "";
        }}
      />
    </div>
  );
};

export default FileUploadArea;
