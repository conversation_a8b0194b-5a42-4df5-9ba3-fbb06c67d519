"use client";

import React, { useState, useEffect } from "react";
import { Sidebar } from "@/components/Dashboard/DashboardV2/components/Sidebar";
import { SidebarSkeleton } from "@/components/Dashboard/DashboardV2/components/SidebarSkeleton";
import supabase from "@/lib/supabaseClient";
import GlobalUpgradeDialog from "@/components/GlobalUpgradeDialog";

/**
 * Common Dashboard layout component with fixed sidebar
 *
 * This component provides a consistent layout for all dashboard pages
 * with a fixed sidebar that doesn't scroll with the content.
 */
export default function DashboardLayout({
  children,
  onFolderChange,
  onSidebarClick,
}) {
  const [isAnonymous, setIsAnonymous] = useState(null); // null 表示未知状态
  const [isSessionLoading, setIsSessionLoading] = useState(true); // 会话加载状态

  useEffect(() => {
    const checkSession = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        setIsAnonymous(session?.user?.is_anonymous || false);
      } catch (error) {
        console.error("Error checking session:", error);
        setIsAnonymous(false); // 出错时默认为非匿名用户
      } finally {
        setIsSessionLoading(false);
      }
    };

    checkSession();
  }, []);

  return (
    <div className="flex h-screen overflow-hidden bg-white">
      {/* Fixed sidebar - 加载状态显示骨架屏，否则根据用户状态显示相应版本 */}
      {isSessionLoading ? (
        <SidebarSkeleton className="hidden md:flex h-screen flex-shrink-0 sticky top-0" />
      ) : (
        <Sidebar
          className="hidden md:flex h-screen flex-shrink-0 sticky top-0"
          isAnonymous={isAnonymous}
          onFolderChange={onFolderChange}
          onSidebarClick={onSidebarClick}
        />
      )}

      {/* Scrollable main content */}
      <div className="flex-1 overflow-y-auto p-4 md:p-8">{children}</div>

      {/* Global UpgradeDialog */}
      <GlobalUpgradeDialog />
    </div>
  );
}
