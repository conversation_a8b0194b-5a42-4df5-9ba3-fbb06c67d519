"use client";

import { useState, useRef } from "react";

import { RecentFiles } from "./RecentFiles";
import { MobileNavBar } from "./MobileNavBar";
import { NewActionView } from "./NewActionView";
import { MobileAccount } from "./MobileAccount";
import UploadDialog from "@/components/Dashboard/UploadDialog";
import {
  YouTubeUploadDialog,
  YouTubeUploadMobilePage,
} from "@/components/Dashboard/YouTubeUpload";
import MaintenanceDialog from "@/components/Common/MaintenanceDialog";
import IncidentDialog from "@/components/Common/IncidentDialog";
import { useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { transcriptionService } from "@/services/api/transcriptionService";
import LimitReachedDialog from "@/components/Dashboard/LimitReachedDialog";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import AppSumoWelcomeDialog from "@/components/Dashboard/AppSumoWelcomeDialog";
import GlobalUpgradeDialog from "@/components/GlobalUpgradeDialog";
import AppSumoActivationErrorDialog from "@/components/Dashboard/AppSumoActivationErrorDialog";
import { useAuthStore } from "@/stores/useAuthStore";
import { useDashboardInitialize } from "@/hooks/useDashboardInitialize";
import { useUserLimits } from "@/hooks/useUserLimits";
import FreeUserUpgradeDialog from "@/components/Dialog/FreeUserUpgradeDialog";

export default function DashboardMobile() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentTab, setCurrentTab] = useState("files");
  const [showNewActions, setShowNewActions] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [showYouTubeMobilePage, setShowYouTubeMobilePage] = useState(false);
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitMessage, setLimitMessage] = useState("");
  const [upgradeSource, setUpgradeSource] = useState("mobile_dashboard");
  const { openDialog } = useUpgradeDialogStore();
  const [showAppSumoWelcomeDialog, setShowAppSumoWelcomeDialog] =
    useState(false);
  const [showFreeUserUpgradeDialog, setShowFreeUserUpgradeDialog] =
    useState(false);
  const [upgradeLimitType, setUpgradeLimitType] = useState(null);
  const [isCheckingLimits, setIsCheckingLimits] = useState(false);
  const youtubeDialogRef = useRef(null);
  const { user, appsumoActivationError, clearAppSumoActivationError } =
    useAuthStore();
  const { checkLimits } = useUserLimits();

  const handleTabChange = (tab) => {
    if (tab === "new") {
      setShowNewActions(true);
    } else {
      setCurrentTab(tab);
      setShowNewActions(false);
    }
  };

  const handleUploadSuccess = async (transcription) => {
    try {
      // Create transcription task
      await transcriptionService.doTranscription(transcription.id);
      // 保留当前文件夹状态，跳转时带上文件夹参数
      const folderParam = searchParams.get("folder");
      const folderQuery =
        folderParam && folderParam !== "all"
          ? `?from_folder=${folderParam}`
          : "";
      router.push(`/transcriptions/${transcription.id}${folderQuery}`);
    } catch (error) {
      console.error("Failed to create transcription task:", error);
    }
    setShowNewActions(false);
    setShowUpload(false);
  };

  const handleYouTubeSubmitSuccess = (transcription) => {
    // 保留当前文件夹状态，跳转时带上文件夹参数
    const folderParam = searchParams.get("folder");
    const folderQuery =
      folderParam && folderParam !== "all" ? `?from_folder=${folderParam}` : "";
    router.push(`/transcriptions/${transcription.id}${folderQuery}`);
    setShowNewActions(false);
  };

  const handleUploadClick = async () => {
    // Check if user is free user (using hasPaidPlan field)
    const isFreeUser = user && !user.hasPaidPlan;

    if (isFreeUser) {
      setIsCheckingLimits(true);
      try {
        // Check user limits before showing upload dialog
        const { canProceed, limitType } = await checkLimits();

        if (!canProceed) {
          setShowNewActions(false); // Close NewActionView
          setUpgradeLimitType(limitType);
          setShowFreeUserUpgradeDialog(true);
          return;
        }
      } finally {
        setIsCheckingLimits(false);
      }
    }

    setShowNewActions(false); // Close NewActionView
    setShowUpload(true);
  };

  const handleYouTubeClick = async () => {
    // Check if user is free user (using hasPaidPlan field)
    const isFreeUser = user && !user.hasPaidPlan;

    if (isFreeUser) {
      setIsCheckingLimits(true);
      try {
        // Check user limits before showing YouTube dialog
        const { canProceed, limitType } = await checkLimits();

        if (!canProceed) {
          setShowNewActions(false); // Close NewActionView
          setUpgradeLimitType(limitType);
          setShowFreeUserUpgradeDialog(true);
          return;
        }
      } finally {
        setIsCheckingLimits(false);
      }
    }

    setShowNewActions(false); // Close NewActionView
    setShowYouTubeMobilePage(true); // Show mobile page instead of dialog
  };

  // 使用共享的 Dashboard 初始化逻辑
  const { refreshKey } = useDashboardInitialize({
    setShowLimitDialog,
    setLimitMessage,
    setUpgradeSource,
    setShowAppSumoWelcomeDialog,
    source: "mobile_dashboard",
  });

  // 处理升级按钮点击
  const handleUpgradeClick = () => {
    setShowLimitDialog(false);
    openDialog({
      source: upgradeSource,
      defaultPlanType: "yearly",
    });
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 pt-4">
      {currentTab === "files" ? (
        <RecentFiles />
      ) : currentTab === "account" ? (
        <MobileAccount key={refreshKey} />
      ) : null}

      <MobileNavBar currentTab={currentTab} onTabChange={handleTabChange} />

      <NewActionView
        isOpen={showNewActions}
        onClose={() => setShowNewActions(false)}
        onUploadClick={handleUploadClick}
        onYouTubeClick={handleYouTubeClick}
        isLoading={isCheckingLimits}
      />

      <UploadDialog
        isOpen={showUpload}
        onOpenChange={setShowUpload}
        onUploadSuccess={handleUploadSuccess}
      />

      <YouTubeUploadDialog
        ref={youtubeDialogRef}
        onTranscribeSubmit={handleYouTubeSubmitSuccess}
      />

      <YouTubeUploadMobilePage
        isOpen={showYouTubeMobilePage}
        onClose={() => setShowYouTubeMobilePage(false)}
        onTranscribeSubmit={handleYouTubeSubmitSuccess}
      />

      <MaintenanceDialog />
      <IncidentDialog />

      {/* 限制提示弹窗 */}
      <LimitReachedDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        onUpgrade={handleUpgradeClick}
        source={upgradeSource}
        message={limitMessage}
      />

      {/* 免费用户升级弹窗 */}
      <FreeUserUpgradeDialog
        isOpen={showFreeUserUpgradeDialog}
        onOpenChange={setShowFreeUserUpgradeDialog}
        limitType={upgradeLimitType}
      />

      {/* AppSumo 欢迎弹窗 */}
      <AppSumoWelcomeDialog
        isOpen={showAppSumoWelcomeDialog}
        onClose={() => setShowAppSumoWelcomeDialog(false)}
        user={user}
      />

      {/* AppSumo 激活错误弹窗 */}
      <AppSumoActivationErrorDialog
        isOpen={!!appsumoActivationError}
        onClose={clearAppSumoActivationError}
        errorCode={appsumoActivationError}
      />

      {/* Global UpgradeDialog */}
      <GlobalUpgradeDialog />
    </div>
  );
}
