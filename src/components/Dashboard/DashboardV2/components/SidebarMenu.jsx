import { useState, useEffect } from "react";
import { House, FolderOpen } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useFolderStore } from "@/stores/useFolderStore";
import { cn } from "@/lib/utils";
import FolderList from "./FolderList";

export default function SidebarMenu({
  className,
  isAnonymous,
  onFolderChange,
}) {
  const t = useTranslations("dashboard.sidebar");
  const router = useRouter();
  const currentPath = usePathname();
  const searchParams = useSearchParams();

  // 判断Home菜单项是否应该被选中
  const isHomeSelected =
    currentPath === "/dashboard" && !searchParams.get("folder");

  // 使用全局状态
  const { setSelectedFolderId } = useFolderStore();

  // 处理 Home 点击
  const handleHomeClick = () => {
    // 更新全局状态
    setSelectedFolderId("all");

    // 检查当前是否在 dashboard 页面
    if (currentPath === "/dashboard") {
      // 在 dashboard 页面，只需要清除 URL 参数并通知父组件
      const url = new URL(window.location);
      url.searchParams.delete("folder");
      window.history.pushState({}, "", url);

      // 通知父组件，使用"all"表示选中Home（获取所有转录记录）
      if (onFolderChange) {
        onFolderChange("all");
      }
    } else {
      // 不在 dashboard 页面，跳转到 dashboard 主页
      router.push("/dashboard");
      // 通知父组件，使用"all"表示选中Home（获取所有转录记录）
      if (onFolderChange) {
        onFolderChange("all");
      }
    }
  };

  if (isAnonymous) {
    return null; // 匿名用户不显示菜单
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Home 菜单项 */}
      <div className="px-4 mb-6">
        <div
          className={`flex items-center justify-between flex-shrink-0 cursor-pointer rounded-lg px-2 py-1 -mx-2 -my-1 transition-colors select-none ${
            isHomeSelected
              ? "bg-[#6366f1]/10 text-[#6366f1]"
              : "hover:bg-gray-50 text-gray-700"
          }`}
          onClick={handleHomeClick}
        >
          <div className="flex items-center gap-2">
            <House
              className={`w-5 h-5 ${
                isHomeSelected ? "text-[#6366f1]" : "text-gray-500"
              }`}
            />
            <span
              className={`font-medium text-sm ${
                isHomeSelected ? "text-[#6366f1]" : "text-gray-700"
              }`}
            >
              {t("home")}
            </span>
          </div>
          {/* 占位 div 保持与 Folders 菜单项相同的高度 */}
          <div className="flex gap-1">
            <div className="w-8 h-8"></div>
            <div className="w-8 h-8"></div>
          </div>
        </div>
      </div>

      {/* FolderList 组件 */}
      <div className="flex-1 min-h-0">
        <FolderList
          isAnonymous={isAnonymous}
          className="h-full"
          onFolderChange={onFolderChange}
        />
      </div>
    </div>
  );
}
