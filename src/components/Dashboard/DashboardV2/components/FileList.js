import {
  Search,
  Trash2,
  <PERSON>old<PERSON><PERSON><PERSON>,
  X,
  Download,
  Upload,
  LinkIcon,
  ChevronDown,
  Grid3X3,
  List,
  MoreHorizontal,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FileTable } from "./FileTable";
import { EmptyState } from "./EmptyState";
import DeleteDialog from "../../DeleteDialog";
import MoveToFolderDialog from "./MoveToFolderDialog";
import ExportDialog from "./ExportDialog";

import {
  useState,
  useCallback,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from "react";
import debounce from "lodash/debounce";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { folderService } from "@/services/api/folderService";
import { transcriptionService } from "@/services/api/transcriptionService";
import { toast } from "sonner";
import { FILE_STATUS, FILE_STATUS_UTILS } from "@/constants/file";
import { trackEvent } from "@/lib/analytics";

export const FileList = forwardRef(function FileList(
  { selectedFolderId, onUploadClick, onYouTubeClick, isCheckingLimits = false },
  ref
) {
  const t = useTranslations("dashboard.fileList");
  const tSidebar = useTranslations("dashboard.sidebar");
  const searchParams = useSearchParams();
  const [searchKeyword, setSearchKeyword] = useState("");
  const [debouncedKeyword, setDebouncedKeyword] = useState("");
  const [folders, setFolders] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState(new Map()); // 改为Map，存储文件ID和文件对象
  const [isBatchMode, setIsBatchMode] = useState(false);
  const [viewMode, setViewMode] = useState("table"); // "table" or "list"

  const [folderRefreshTrigger, setFolderRefreshTrigger] = useState(0);

  // 批量操作相关状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showMoveDialog, setShowMoveDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");

  // 从URL参数或props获取当前选中的文件夹
  // 优先使用URL参数，确保页面刷新后能正确获取文件夹ID
  const currentFolderId = (() => {
    const folderParam = searchParams.get("folder");
    if (folderParam) {
      return folderParam;
    }
    // 默认使用"all"表示获取所有转录记录（Home状态）
    return selectedFolderId || "all";
  })();

  // 获取文件夹列表
  const fetchFolders = useCallback(async () => {
    try {
      const response = await folderService.getFolders();
      const { folders: apiFolders } = response.data;

      // 合并默认的"All Transcriptions"文件夹和API返回的文件夹
      const allFolders = [
        {
          id: "all",
          name: tSidebar("allTranscriptionsTitle"),
          isDefault: true,
        },
        ...apiFolders,
      ];
      setFolders(allFolders);
    } catch (error) {
      console.error("Error fetching folders:", error);
    }
  }, []);

  // 获取当前文件夹名称
  const getCurrentFolderName = useCallback(() => {
    // 如果selectedFolderId是"all"，显示"All Transcriptions"（Home状态）
    if (selectedFolderId === "all") {
      return tSidebar("allTranscriptionsTitle");
    }

    // 如果selectedFolderId是"unclassified"，显示"Uncategorized"
    if (selectedFolderId === "unclassified") {
      return tSidebar("defaultFolderName");
    }

    const folder = folders.find((f) => f.id === currentFolderId);

    if (folder) {
      return folder.name;
    }

    // 如果找不到文件夹且当前是 'all'，返回国际化的 "All Transcriptions"
    if (currentFolderId === "all") {
      return tSidebar("allTranscriptionsTitle");
    }
    // 其他情况返回默认值
    return tSidebar("allTranscriptionsTitle");
  }, [folders, currentFolderId, selectedFolderId, tSidebar]);

  // 获取文件夹列表 - 在组件挂载或手动刷新时触发
  useEffect(() => {
    fetchFolders();
  }, [fetchFolders, folderRefreshTrigger]);

  // 监听文件夹变化事件
  useEffect(() => {
    const handleFolderChange = () => {
      setFolderRefreshTrigger((prev) => prev + 1);
    };

    window.addEventListener("folderListChanged", handleFolderChange);
    return () => {
      window.removeEventListener("folderListChanged", handleFolderChange);
    };
  }, []);

  // 创建一个防抖的搜索处理函数，提升搜索用户体验
  const debouncedSearch = useCallback(
    debounce((value) => {
      setDebouncedKeyword(value);
    }, 500),
    []
  );

  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchKeyword(value); // 立即更新输入框的值
    debouncedSearch(value); // 延迟更新搜索关键词
  };

  // 批量操作处理函数
  const handleEnterBatchMode = () => {
    // 打点记录进入批量操作模式
    trackEvent("batch_operation_button_clicked");
    setIsBatchMode(true);
    setSelectedFiles(new Map()); // 清空之前的选择
  };

  const handleExitBatchMode = () => {
    setIsBatchMode(false);
    setSelectedFiles(new Map()); // 清空选择
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    exitBatchMode: handleExitBatchMode,
    isBatchMode: isBatchMode,
  }));

  const handleBatchDelete = () => {
    if (selectedFiles.size === 0) return;

    // 打点记录批量删除按钮点击
    trackEvent("batch_delete_button_clicked", {
      selectedCount: selectedFiles.size,
    });

    // 检查是否超过最大限制（100个文件）
    if (selectedFiles.size > 100) {
      setErrorMessage(t("errors.tooManyFiles", { max: 100 }));
      setTimeout(() => setErrorMessage(""), 5000); // 5秒后清除错误消息
      return;
    }

    // 检查选中的文件中是否有正在处理中的转录记录
    const selectedFileObjects = Array.from(selectedFiles.values());

    const processingFiles = selectedFileObjects.filter(
      (file) => !FILE_STATUS_UTILS.canDelete(file.status)
    );

    if (processingFiles.length > 0) {
      setErrorMessage(t("errors.cannotDeleteProcessing"));
      setTimeout(() => setErrorMessage(""), 5000); // 5秒后清除错误消息
      return;
    }

    // 显示确认对话框
    setShowDeleteDialog(true);
  };

  const handleConfirmBatchDelete = async () => {
    if (selectedFiles.size === 0) return;

    setIsDeleting(true);
    try {
      // 获取选中文件的ID数组，保持字符串格式
      // 重要：由于转录记录ID是64位大整数，使用字符串格式避免JavaScript精度丢失
      const transcriptionIds = Array.from(selectedFiles.keys());

      const response = await transcriptionService.batchDeleteTranscriptions(
        transcriptionIds
      );
      const result = response.data;

      // 显示成功提示
      toast.success(t("batchDeleteSuccess", { count: result.deleted }));

      // 如果有成功删除的文件，需要刷新文件列表
      if (result.deleted > 0) {
        // 触发文件列表刷新
        setRefreshTrigger((prev) => prev + 1);
        setSelectedFiles(new Map());
        setIsBatchMode(false);
      }

      // 关闭对话框
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Batch delete failed:", error);
      // 显示错误提示
      toast.error(t("batchDeleteError"));

      // 关闭对话框
      setShowDeleteDialog(false);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCloseDeleteDialog = (open) => {
    setShowDeleteDialog(open);
  };

  const handleBatchMove = () => {
    if (selectedFiles.size === 0) return;
    // 打点记录批量移动按钮点击
    trackEvent("batch_move_button_clicked", {
      selectedCount: selectedFiles.size,
    });
    setShowMoveDialog(true);
  };

  const handleBatchExport = () => {
    if (selectedFiles.size === 0) return;

    // 打点记录批量导出按钮点击
    trackEvent("batch_export_button_clicked", {
      selectedCount: selectedFiles.size,
    });

    // 检查是否超过最大限制（50个文件）
    if (selectedFiles.size > 50) {
      setErrorMessage(t("errors.tooManyFilesForExport", { max: 50 }));
      setTimeout(() => setErrorMessage(""), 5000); // 5秒后清除错误消息
      return;
    }

    // 检查选中的文件中是否有不能导出的转录记录（只有完成且成功的才能导出）
    const selectedFileObjects = Array.from(selectedFiles.values());

    const nonExportableFiles = selectedFileObjects.filter(
      (file) => file.status !== FILE_STATUS.COMPLETED
    );

    if (nonExportableFiles.length > 0) {
      setErrorMessage(t("errors.cannotExportProcessing"));
      setTimeout(() => setErrorMessage(""), 5000); // 5秒后清除错误消息
      return;
    }

    // 显示导出对话框
    setShowExportDialog(true);
  };

  const handleCloseMoveDialog = () => {
    setShowMoveDialog(false);
  };

  const handleCloseExportDialog = () => {
    setShowExportDialog(false);
    // 注意：取消导出对话框时不退出批量模式，保持用户的选择状态
  };

  const handleExportSuccess = () => {
    // 关闭导出对话框
    setShowExportDialog(false);

    // 批量导出成功，清空选择并退出批量模式
    setSelectedFiles(new Map());
    setIsBatchMode(false);
  };

  const handleMoveSuccess = (_result, _folderId, isBatchMode) => {
    // 关闭移动对话框
    setShowMoveDialog(false);

    // 刷新文件列表
    setRefreshTrigger((prev) => prev + 1);

    if (isBatchMode) {
      // 批量移动成功，清空选择并退出批量模式
      setSelectedFiles(new Map());
      setIsBatchMode(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* 新的 Header 布局 */}
      <div className="space-y-4 mb-6">
        {/* 第一行：标题和搜索框 */}
        <div className="flex items-center justify-between">
          {/* 左侧：当前文件夹名称 */}
          <h1
            className="text-xl font-medium truncate max-w-md"
            title={getCurrentFolderName()}
          >
            {getCurrentFolderName()}
          </h1>

          {/* 右侧：搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder={t("searchPlaceholder")}
              className="pl-10 w-64"
              value={searchKeyword}
              onChange={handleSearch}
            />
          </div>
        </div>

        {/* 第二行：操作按钮 */}
        <div className="flex items-center justify-between">
          {/* 左侧：Upload File 和 YouTube Link 按钮 - 批量模式下隐藏 */}
          {!isBatchMode && (
            <div className="flex items-center gap-3">
              <Button
                onClick={onUploadClick}
                disabled={isCheckingLimits}
                className="bg-custom-bg hover:bg-custom-bg/90 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Upload className="w-4 h-4" />
                {t("uploadFile")}
              </Button>
              <Button
                onClick={onYouTubeClick}
                disabled={isCheckingLimits}
                variant="outline"
                className="flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <LinkIcon className="w-4 h-4" />
                {t("youtubeLink")}
              </Button>
            </div>
          )}

          {/* 右侧：Table View 下拉和 Bulk Actions 按钮 */}
          <div className="flex items-center gap-2">
            {!isBatchMode && (
              <Button
                variant="ghost"
                size="sm"
                className="gap-2 text-muted-foreground hover:text-foreground h-8"
                onClick={handleEnterBatchMode}
              >
                <MoreHorizontal className="w-4 h-4" />
                {t("bulkActions")}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 错误消息 */}
      {errorMessage && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      {/* 批量操作卡片 - 只在批量模式下显示 */}
      {isBatchMode && (
        <div className="flex items-center justify-between mb-4 py-4">
          {/* 左侧：选择计数 */}
          <div className="flex items-center">
            <span className="text-sm text-gray-500">
              {t("selectedCount", { count: selectedFiles.size })}
            </span>
          </div>

          {/* 右侧：批量操作按钮 */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBatchMove}
              disabled={selectedFiles.size === 0}
              className="text-blue-600 hover:text-blue-700 hover:bg-transparent h-auto text-sm font-normal"
            >
              <FolderOpen className="w-4 h-4" />
              {t("move")}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBatchExport}
              disabled={selectedFiles.size === 0}
              className="text-green-600 hover:text-green-700 hover:bg-transparent h-auto text-sm font-normal"
            >
              <Download className="w-4 h-4" />
              {t("export")}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBatchDelete}
              disabled={selectedFiles.size === 0}
              className="text-red-600 hover:text-red-700 hover:bg-transparent h-auto text-sm font-normal"
            >
              <Trash2 className="w-4 h-4" />
              {t("delete")}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExitBatchMode}
              className="text-gray-600 hover:text-gray-700 hover:bg-transparent h-auto text-sm font-normal"
            >
              <X className="w-4 h-4" />
              {t("cancel")}
            </Button>
          </div>
        </div>
      )}

      {/* 文件表格 */}
      <div className="flex-1 overflow-hidden">
        <FileTable
          searchKeyword={debouncedKeyword}
          selectedFolderId={currentFolderId}
          selectedFiles={selectedFiles}
          onFileSelectionChange={setSelectedFiles}
          isBatchMode={isBatchMode}
          renderEmpty={() => <EmptyState />}
          refreshTrigger={refreshTrigger}
          folders={folders}
        />
      </div>

      {/* 批量删除确认对话框 */}
      <DeleteDialog
        isOpen={showDeleteDialog}
        onOpenChange={handleCloseDeleteDialog}
        onDelete={handleConfirmBatchDelete}
        isBatch={true}
        selectedCount={selectedFiles.size}
        isLoading={isDeleting}
      />

      {/* 批量移动对话框 */}
      <MoveToFolderDialog
        isOpen={showMoveDialog}
        onClose={handleCloseMoveDialog}
        transcriptionIds={Array.from(selectedFiles.keys())}
        currentFolderId={currentFolderId}
        onMoveSuccess={handleMoveSuccess}
      />

      {/* 批量导出对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onOpenChange={(open) => {
          if (!open) {
            handleCloseExportDialog();
          }
        }}
        fileIds={Array.from(selectedFiles.keys())}
        isBatchMode={true}
        selectedCount={selectedFiles.size}
        exportSource="filelist_action"
        onExportComplete={handleExportSuccess}
      />
    </div>
  );
});
