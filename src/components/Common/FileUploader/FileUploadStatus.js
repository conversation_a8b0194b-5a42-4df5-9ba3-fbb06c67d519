import { CheckCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { FILE_STATUS } from "@/constants/file";
import { useRouter, useSearchParams } from "@/i18n/navigation";
import { useAuthStore } from "@/stores/useAuthStore";

const FileUploadStatus = ({
  uploadState,
  uploadProgress,
  processingProgress,
  processing,
  onTranscribeClick,
  showAlert,
  errorMessage,
  errorAction = null,
  onErrorAction = () => {},
  selectedFile,
  isTranscribing = false,
  // 新增：网速和剩余时间相关 props
  uploadSpeed = 0,
  estimatedTimeRemaining = 0,
  isMultipartUpload = false,
  // 新增：文件夹ID参数
  selectedFolderId = null,
}) => {
  const t = useTranslations("common.fileUploadStatus");
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuthStore();

  // 格式化网速显示
  const formatSpeed = (bytesPerSecond) => {
    if (bytesPerSecond === 0) return "";

    const gbps = bytesPerSecond / (1024 * 1024 * 1024);
    if (gbps >= 1) {
      return `${gbps.toFixed(1)} GB/s`;
    }

    const mbps = bytesPerSecond / (1024 * 1024);
    if (mbps >= 1) {
      return `${mbps.toFixed(1)} MB/s`;
    }

    const kbps = bytesPerSecond / 1024;
    if (kbps >= 1) {
      return `${kbps.toFixed(0)} KB/s`;
    }

    return `${bytesPerSecond.toFixed(0)} B/s`;
  };

  // 格式化剩余时间显示
  const formatTimeRemaining = (seconds) => {
    if (seconds === 0 || !isFinite(seconds)) return "";

    // 对于很小的时间，显示"即将完成"
    if (seconds < 5) {
      return t("almostDone");
    }

    if (seconds < 60) {
      return `${Math.ceil(seconds)}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.ceil(seconds % 60);
      // 只显示分钟，简化显示
      return remainingSeconds > 30 ? `${minutes + 1}m` : `${minutes}m`;
    } else if (seconds < 86400) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return minutes > 30 ? `${hours + 1}h` : `${hours}h`;
    } else {
      // 超过24小时，显示天数
      const days = Math.floor(seconds / 86400);
      return `${days}d`;
    }
  };

  const getFileAction = (selectedFile) => {
    const isViewable = [
      FILE_STATUS.PREPROCESSING,
      FILE_STATUS.PREPROCESSING_FAILED,
      FILE_STATUS.PROCESSING,
      FILE_STATUS.PARTIALLY_COMPLETED,
      FILE_STATUS.COMPLETED_WITH_ERRORS,
      FILE_STATUS.COMPLETED,
      FILE_STATUS.FAILED,
    ].includes(selectedFile?.status);

    // 根据用户状态决定转录按钮文案
    const getTranscribeLabel = () => {
      // 如果用户不存在或者是匿名用户，显示"免费转录"
      if (!user || user.isAnonymous) {
        return t("transcribeForFree");
      }
      // 真正的登录用户显示"转录"
      return t("transcribe");
    };

    return {
      action: isViewable
        ? () => {
            // 保留当前文件夹状态，跳转时带上文件夹参数
            const folderParam = searchParams.get("folder") || selectedFolderId;
            const folderQuery =
              folderParam && folderParam !== "all"
                ? `?from_folder=${folderParam}`
                : "";
            router.push(`/transcriptions/${selectedFile.id}${folderQuery}`);
          }
        : onTranscribeClick,
      label: isViewable ? t("viewTranscript") : getTranscribeLabel(),
    };
  };

  const { action, label } = getFileAction(selectedFile);

  if (showAlert) {
    return (
      <Alert variant="destructive" className="border-0">
        <AlertDescription className="flex flex-col items-center gap-5">
          {errorAction && (
            <Button
              className="w-full bg-custom-bg hover:bg-custom-bg/90"
              onClick={onErrorAction}
            >
              {errorAction}
            </Button>
          )}
          <span className="text-center">
            {errorMessage || t("defaultError")}
          </span>
        </AlertDescription>
      </Alert>
    );
  }

  if (uploadState === "preparing") {
    return (
      <div className="flex items-center justify-center space-x-2 p-4 bg-custom-bg/10 rounded-md transition-opacity duration-300 ease-in-out">
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-custom-bg border-t-transparent"></div>
        <span className="text-sm text-custom-bg">{t("preparing")}</span>
      </div>
    );
  }

  if (processing) {
    return (
      <div className="relative h-12 rounded-md bg-custom-bg/20 overflow-hidden">
        <div
          className="absolute inset-y-0 left-0 bg-custom-bg transition-all duration-300 ease-in-out"
          style={{ width: `${processingProgress}%` }}
        />
        <div className="absolute inset-0 flex items-center justify-center text-white font-medium">
          {t("processing", { progress: processingProgress })}
        </div>
      </div>
    );
  }

  if (uploadState === "uploading") {
    const speedText = formatSpeed(uploadSpeed);
    const timeText = formatTimeRemaining(estimatedTimeRemaining);
    const showSpeedInfo = isMultipartUpload && (speedText || timeText);

    return (
      <div className="space-y-2">
        <div className="relative h-12 rounded-md bg-custom-bg/20 overflow-hidden">
          <div
            className="absolute inset-y-0 left-0 bg-custom-bg transition-all duration-300 ease-in-out"
            style={{ width: `${uploadProgress}%` }}
          />
          <div className="absolute inset-0 flex items-center justify-center text-white font-medium">
            {t("uploading", { progress: uploadProgress })}
          </div>
        </div>

        {/* 显示网速和剩余时间（仅用于 multipart 上传） */}
        {showSpeedInfo && (
          <div className="flex justify-between text-sm text-gray-600 px-1">
            {speedText && (
              <span>
                {t("uploadSpeed")}: {speedText}
              </span>
            )}
            {timeText && (
              <span>
                {t("timeRemaining")}: {timeText}
              </span>
            )}
          </div>
        )}
      </div>
    );
  }

  if (uploadState === "completing") {
    return (
      <div className="flex items-center justify-center space-x-2 p-4 bg-custom-bg/10 rounded-md">
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-custom-bg border-t-transparent"></div>
        <span className="text-sm text-custom-bg">{t("completing")}</span>
      </div>
    );
  }

  if (uploadState === "success") {
    return (
      <>
        <Alert className="mb-0 border-0">
          <div className="flex items-center justify-center gap-2">
            <CheckCircle className="h-4 w-4 text-custom-bg" strokeWidth={2.5} />
            <AlertDescription className="text-sm text-custom-bg">
              {t("success")}
            </AlertDescription>
          </div>
        </Alert>
        <Button
          className="w-full h-10 px-6 text-white bg-custom-bg hover:bg-custom-bg/90 transition-colors"
          onClick={action}
          disabled={isTranscribing}
        >
          {isTranscribing ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              <span className="ml-2">{t("process")}</span>
            </div>
          ) : (
            label
          )}
        </Button>
      </>
    );
  }

  return null;
};

export default FileUploadStatus;
